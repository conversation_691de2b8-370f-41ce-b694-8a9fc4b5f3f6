<template>
  <HerbitProfessionalLayout
    title="Add Fixed Assessment Questions"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="indigo">
          <form @submit.prevent="addFixedQuestions" class="space-y-6">
            <!-- Assessment Selection -->
            <div>
              <Label for="assessmentSelect" class="text-gray-300">Select Fixed Assessment</Label>
              <select
                id="assessmentSelect"
                v-model="selectedAssessmentId"
                class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                required
                @change="fetchAssessmentDetails"
              >
                <option value="" disabled>Select a fixed assessment</option>
                <option v-for="assessment in fixedAssessments" :key="assessment.id" :value="assessment.id">
                  {{ assessment.id }}: {{ assessment.name }}
                </option>
              </select>
            </div>

            <!-- Manual Question Number Input -->
            <div>
              <Label for="questionNumbers" class="text-gray-300">Question Numbers (Manual Entry)</Label>
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <Label class="text-xs text-green-400">Easy (min: 6)</Label>
                  <Input
                    type="number"
                    v-model.number="manualQuestionCounts.easy"
                    min="6"
                    placeholder="6"
                  />
                </div>
                <div>
                  <Label class="text-xs text-yellow-400">Intermediate (min: 6)</Label>
                  <Input
                    type="number"
                    v-model.number="manualQuestionCounts.intermediate"
                    min="6"
                    placeholder="6"
                  />
                </div>
                <div>
                  <Label class="text-xs text-red-400">Advanced (min: 8)</Label>
                  <Input
                    type="number"
                    v-model.number="manualQuestionCounts.advanced"
                    min="8"
                    placeholder="8"
                  />
                </div>
              </div>
              <div class="mt-2 text-xs text-gray-400">
                <p><strong>Total Questions:</strong> {{ getTotalManualQuestions() }}</p>
                <p><strong>Minimum Requirements:</strong> Easy: 6, Intermediate: 6, Advanced: 8 (Total: 20)</p>
              </div>
            </div>



            <!-- Selected Questions Summary -->
            <div>
              <div class="flex justify-between items-center mb-1">
                <Label class="text-gray-300">Selected Questions</Label>
                <span class="text-xs text-indigo-300">
                  {{ getSelectedQuestionCount() }} selected
                </span>
              </div>
              <div class="bg-gray-800/50 border border-gray-700 rounded-lg p-3 min-h-[60px]">
                <div v-if="getSelectedQuestionCount() === 0" class="text-gray-500 text-sm italic">
                  No questions selected. Select questions from the list below.
                </div>
                <div v-else class="flex flex-wrap gap-2">
                  <div
                    v-for="id in getSelectedQuestionIds()"
                    :key="id"
                    class="flex items-center bg-indigo-900/40 text-indigo-200 text-xs px-2 py-1 rounded"
                  >
                    <span>ID: {{ id }}</span>
                    <Button
                      @click="removeQuestionFromSelection(id)"
                      variant="ghost"
                      size="xs"
                      class="ml-2 text-indigo-300 hover:text-indigo-100"
                    >
                      ×
                    </Button>
                  </div>
                </div>
              </div>
              <div class="text-xs text-indigo-300 mt-1">
                <p><strong>Note:</strong> These questions will be used for all sessions of this assessment.</p>
              </div>
            </div>

            <!-- Available Questions -->
            <div v-if="availableQuestions.length > 0" class="mt-4">
              <div class="flex justify-between items-center mb-2">
                <div class="flex items-center space-x-3">
                  <h3 class="text-sm font-medium text-gray-300">Available Questions</h3>
                  <!-- Random Selection Button -->
                  <Button
                    @click.prevent="selectRandomQuestions"
                    variant="generalAction"
                    size="skillButton"
                    :disabled="!assessmentDetails || availableQuestions.length === 0"
                    title="Randomly select questions based on assessment requirements"
                  >
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
                      </svg>
                      Random Select
                    </span>
                  </Button>
                </div>
                <div class="flex space-x-2">
                  <!-- Filter by difficulty -->
                  <select
                    v-model="difficultyFilter"
                    class="text-xs px-2 py-1 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  >
                    <option value="all">All Difficulties</option>
                    <option value="easy">Easy</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>

                  <!-- Search input -->
                  <div class="relative">
                    <Input
                      v-model="searchQuery"
                      placeholder="Search questions..."
                      class="text-xs pl-7 pr-2 py-1 w-40"
                    />
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 absolute left-2 top-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div class="bg-gray-800/50 border border-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                <div v-if="filteredQuestions.length === 0" class="text-center py-4 text-gray-400">
                  No questions match your filters.
                </div>
                <div v-else class="space-y-4">
                  <div v-for="question in filteredQuestions" :key="question.que_id"
                       class="p-3 border border-gray-700 rounded-lg hover:border-indigo-500/50 transition-all"
                       :class="{ 'border-indigo-500/50 bg-indigo-900/10': isQuestionSelected(question.que_id) }">
                    <div class="flex justify-between">
                      <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 text-xs rounded-full"
                              :class="{
                                'bg-green-900/50 text-green-400': question.level === 'easy',
                                'bg-yellow-900/50 text-yellow-400': question.level === 'intermediate',
                                'bg-red-900/50 text-red-400': question.level === 'advanced'
                              }">
                          {{ question.level.charAt(0).toUpperCase() + question.level.slice(1) }}
                        </span>
                        <span class="text-gray-400 text-sm">ID: {{ question.que_id }}</span>
                        <span class="text-gray-400 text-sm">{{ question.skill_name }}</span>
                      </div>
                      <Button
                        @click="addQuestionToSelection(question.que_id)"
                        variant="generalAction"
                        size="skillButton"
                        :class="isQuestionSelected(question.que_id)
                          ? 'bg-indigo-700/70 text-indigo-200 hover:bg-indigo-600/70'
                          : 'bg-indigo-900/50 text-indigo-400 hover:bg-indigo-800/50'"
                      >
                        {{ isQuestionSelected(question.que_id) ? 'Selected' : 'Select' }}
                      </Button>
                    </div>
                    <div class="mt-2 text-white text-sm">{{ question.question }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Loading indicator -->
            <div v-if="isLoading" class="flex justify-center items-center py-4">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
              <span class="ml-3 text-gray-300">Adding questions...</span>
            </div>

            <!-- Error/Success message -->
            <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
              <AlertDescription>{{ message }}</AlertDescription>
            </Alert>

            <!-- Submit button -->
            <div class="flex justify-between items-center">
              <div class="text-sm text-gray-400">
                <span v-if="getSelectedQuestionCount() > 0">
                  {{ getSelectedQuestionCount() }} questions selected
                  <span v-if="getTotalManualQuestions() > 0"
                        :class="{
                          'text-green-400': getSelectedQuestionCount() === getTotalManualQuestions(),
                          'text-yellow-400': getSelectedQuestionCount() < getTotalManualQuestions(),
                          'text-red-400': getSelectedQuestionCount() > getTotalManualQuestions()
                        }">
                    ({{ getTotalManualQuestions() }} required)
                  </span>
                </span>
              </div>
              <Button
                type="submit"
                variant="assessmentGenerate"
                size="skillButton"
                :disabled="isLoading || !selectedAssessmentId || getSelectedQuestionCount() === 0"
              >
                <span class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Save Questions
                </span>
              </Button>
            </div>
          </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Form data
const selectedAssessmentId = ref('');
const questionIds = ref('');
const assessments = ref([]);
const assessmentDetails = ref(null);
const availableQuestions = ref([]);
const isLoading = ref(false);

// Manual question counts
const manualQuestionCounts = ref({
  easy: 6,
  intermediate: 6,
  advanced: 8
});

// Filtering
const searchQuery = ref('');
const difficultyFilter = ref('all');

// Computed property to filter only fixed assessments
const fixedAssessments = computed(() => {
  return assessments.value.filter(assessment =>
    assessment.question_selection_mode === 'fixed'
  );
});

// Computed property for filtered questions
const filteredQuestions = computed(() => {
  return availableQuestions.value.filter(question => {
    // Filter by difficulty
    if (difficultyFilter.value !== 'all' && question.level !== difficultyFilter.value) {
      return false;
    }

    // Filter by search query
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase();
      return (
        question.question.toLowerCase().includes(query) ||
        question.que_id.toString().includes(query) ||
        (question.skill_name && question.skill_name.toLowerCase().includes(query))
      );
    }

    return true;
  });
});

// Fetch assessments from API
const fetchAssessments = async () => {
  isLoading.value = true;
  clearMessage();

  try {
    const response = await api.admin.getAssessments();
    assessments.value = response.data.assessments || [];

    if (fixedAssessments.value.length === 0) {
      setErrorMessage('No fixed assessments found. Please create an assessment with "Fixed" question selection mode first.');
    }
  } catch (error) {
    logError(error, 'fetchAssessments');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch assessments'));
  } finally {
    isLoading.value = false;
  }
};

// Fetch assessment details and questions when an assessment is selected
const fetchAssessmentDetails = async () => {
  if (!selectedAssessmentId.value) {
    assessmentDetails.value = null;
    return;
  }

  isLoading.value = true;
  clearMessage();

  try {
    // Fetch basic assessment details
    const detailsResponse = await api.admin.getAssessment(selectedAssessmentId.value);
    assessmentDetails.value = detailsResponse.data;

    // Fetch questions for this assessment
    const questionsResponse = await api.admin.getAssessmentQuestions(selectedAssessmentId.value);
    const questionData = questionsResponse.data;

    // Update the question IDs field with already assigned questions
    if (questionData.assigned_question_ids && questionData.assigned_question_ids.length > 0) {
      questionIds.value = questionData.assigned_question_ids.join(', ');
    } else {
      questionIds.value = '';
    }

    // Store available questions for potential UI enhancement
    availableQuestions.value = questionData.questions || [];

  } catch (error) {
    logError(error, 'fetchAssessmentDetails');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch assessment details'));
    assessmentDetails.value = null;
  } finally {
    isLoading.value = false;
  }
};

// Add fixed questions via API
const addFixedQuestions = async () => {
  if (!selectedAssessmentId.value) {
    setErrorMessage('Please select an assessment');
    return;
  }

  const selectedIds = getSelectedQuestionIds();
  if (selectedIds.length === 0) {
    setErrorMessage('Please select at least one question');
    return;
  }

  // Validate minimum requirements
  if (manualQuestionCounts.value.easy < 6) {
    setErrorMessage('Easy questions must be at least 6.');
    return;
  }
  if (manualQuestionCounts.value.intermediate < 6) {
    setErrorMessage('Intermediate questions must be at least 6.');
    return;
  }
  if (manualQuestionCounts.value.advanced < 8) {
    setErrorMessage('Advanced questions must be at least 8.');
    return;
  }

  isLoading.value = true;
  clearMessage();

  try {
    // Call the API to add fixed questions
    const response = await api.admin.addFinalQuestions({
      assessment_id: parseInt(selectedAssessmentId.value),
      question_ids: selectedIds,
      quiz_name: assessmentDetails.value?.name || 'Assessment',
      question_distribution: {
        easy: manualQuestionCounts.value.easy,
        intermediate: manualQuestionCounts.value.intermediate,
        advanced: manualQuestionCounts.value.advanced,
        total: getTotalManualQuestions()
      }
    });

    setSuccessMessage(response.data.message || `Successfully assigned ${selectedIds.length} questions to the assessment!`);

    // Don't reset the form after success to allow for further edits
    // Instead, refresh the assessment details to show the updated selection
    await fetchAssessmentDetails();

  } catch (error) {
    logError(error, 'addFixedQuestions');
    setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while assigning questions'));
  } finally {
    isLoading.value = false;
  }
};

// Helper methods for question selection
const getSelectedQuestionIds = () => {
  return questionIds.value.split(',')
    .map(id => id.trim())
    .filter(id => id)
    .map(id => parseInt(id));
};

const getSelectedQuestionCount = () => {
  return getSelectedQuestionIds().length;
};

// Helper method to calculate total manual questions
const getTotalManualQuestions = () => {
  return (manualQuestionCounts.value.easy || 0) +
         (manualQuestionCounts.value.intermediate || 0) +
         (manualQuestionCounts.value.advanced || 0);
};

const isQuestionSelected = (questionId) => {
  return getSelectedQuestionIds().includes(questionId);
};

const addQuestionToSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();

  if (selectedIds.includes(questionId)) {
    // Remove the question if already selected
    removeQuestionFromSelection(questionId);
  } else {
    // Add the question if not already selected
    selectedIds.push(questionId);
    questionIds.value = selectedIds.join(', ');
  }
};

const removeQuestionFromSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();
  const updatedIds = selectedIds.filter(id => id !== questionId);
  questionIds.value = updatedIds.join(', ');
};

// Randomly select questions based on assessment requirements
const selectRandomQuestions = () => {
  // Validate manual question counts meet minimum requirements
  if (manualQuestionCounts.value.easy < 6) {
    setErrorMessage('Easy questions must be at least 6.');
    return;
  }
  if (manualQuestionCounts.value.intermediate < 6) {
    setErrorMessage('Intermediate questions must be at least 6.');
    return;
  }
  if (manualQuestionCounts.value.advanced < 8) {
    setErrorMessage('Advanced questions must be at least 8.');
    return;
  }

  // Clear current selection
  questionIds.value = '';

  // Get counts from manual input
  const easyCount = manualQuestionCounts.value.easy;
  const intermediateCount = manualQuestionCounts.value.intermediate;
  const advancedCount = manualQuestionCounts.value.advanced;

  // Group available questions by difficulty
  const easyQuestions = availableQuestions.value.filter(q => q.level === 'easy');
  const intermediateQuestions = availableQuestions.value.filter(q => q.level === 'intermediate');
  const advancedQuestions = availableQuestions.value.filter(q => q.level === 'advanced');

  // Check if we have enough questions of each difficulty
  if (easyQuestions.length < easyCount) {
    setErrorMessage(`Not enough easy questions available. Need ${easyCount}, but only have ${easyQuestions.length}.`);
    return;
  }

  if (intermediateQuestions.length < intermediateCount) {
    setErrorMessage(`Not enough intermediate questions available. Need ${intermediateCount}, but only have ${intermediateQuestions.length}.`);
    return;
  }

  if (advancedQuestions.length < advancedCount) {
    setErrorMessage(`Not enough advanced questions available. Need ${advancedCount}, but only have ${advancedQuestions.length}.`);
    return;
  }

  // Helper function to shuffle array
  const shuffleArray = (array) => {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  };

  // Shuffle and select questions by difficulty
  const selectedEasy = shuffleArray([...easyQuestions]).slice(0, easyCount);
  const selectedIntermediate = shuffleArray([...intermediateQuestions]).slice(0, intermediateCount);
  const selectedAdvanced = shuffleArray([...advancedQuestions]).slice(0, advancedCount);

  // Combine all selected questions
  const selectedQuestions = [...selectedEasy, ...selectedIntermediate, ...selectedAdvanced];

  // Update the selection
  questionIds.value = selectedQuestions.map(q => q.que_id).join(', ');

  // Show success message
  setSuccessMessage(`Randomly selected ${selectedQuestions.length} questions based on assessment requirements.`);
};

onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
